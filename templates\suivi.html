{% extends "base.html" %}

{% block title %}Suivi - {{ user.username }}{% endblock %}

{% block content %}
<div class="text-center mb-5">
    <h1 class="display-5 fw-bold text-primary mb-3">
        <i class="bi bi-graph-up"></i>
        Suivi de sommeil - {{ user.username }}
    </h1>
    <p class="lead text-muted">
        Votre historique et statistiques de sommeil
    </p>
</div>

<!-- Statistiques générales -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center border-primary">
            <div class="card-body">
                <i class="bi bi-clock text-primary" style="font-size: 2rem;"></i>
                <h4 class="mt-2 text-primary">{{ moyenne_sommeil }}h</h4>
                <p class="text-muted mb-0">Moyenne de sommeil</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-warning">
            <div class="card-body">
                <i class="bi bi-star text-warning" style="font-size: 2rem;"></i>
                <h4 class="mt-2 text-warning">{{ moyenne_qualite }}/5</h4>
                <p class="text-muted mb-0">Qualité moyenne</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-success">
            <div class="card-body">
                <i class="bi bi-calendar-check text-success" style="font-size: 2rem;"></i>
                <h4 class="mt-2 text-success">{{ records|length }}</h4>
                <p class="text-muted mb-0">Nuits enregistrées</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-info">
            <div class="card-body">
                <i class="bi bi-person text-info" style="font-size: 2rem;"></i>
                <h4 class="mt-2 text-info">{{ user.age }} ans</h4>
                <p class="text-muted mb-0">Âge</p>
            </div>
        </div>
    </div>
</div>

<!-- Actions rapides -->
<div class="row mb-4">
    <div class="col-12 text-center">
        <a href="{{ url_for('tracker') }}" class="btn btn-primary btn-lg me-3">
            <i class="bi bi-plus-circle"></i>
            Ajouter une nuit
        </a>
        <a href="{{ url_for('rappels', username=user.username) }}" class="btn btn-outline-warning btn-lg">
            <i class="bi bi-alarm"></i>
            Gérer les rappels
        </a>
    </div>
</div>

<!-- Historique des nuits -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">
                    <i class="bi bi-calendar-week"></i>
                    Historique des 30 dernières nuits
                </h4>
            </div>
            <div class="card-body">
                {% if records %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th><i class="bi bi-calendar"></i> Date</th>
                                    <th><i class="bi bi-moon"></i> Coucher</th>
                                    <th><i class="bi bi-sun"></i> Réveil</th>
                                    <th><i class="bi bi-clock"></i> Durée</th>
                                    <th><i class="bi bi-star"></i> Qualité</th>
                                    <th><i class="bi bi-journal-text"></i> Notes</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for record in records %}
                                <tr>
                                    <td>
                                        <strong>{{ record.date }}</strong>
                                        <br>
                                        <small class="text-muted">
                                            {{ record.date }}
                                        </small>
                                    </td>
                                    <td>
                                        <span class="badge bg-dark">{{ record.bedtime }}</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-warning">{{ record.wake_time }}</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary">{{ record.sleep_duration }}h</span>
                                    </td>
                                    <td>
                                        {% if record.sleep_quality %}
                                            {% for i in range(record.sleep_quality) %}⭐{% endfor %}
                                            <br>
                                            <small class="text-muted">{{ record.sleep_quality }}/5</small>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if record.notes %}
                                            <small>{{ record.notes[:50] }}{% if record.notes|length > 50 %}...{% endif %}</small>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="bi bi-moon text-muted" style="font-size: 4rem;"></i>
                        <h4 class="text-muted mt-3">Aucune donnée de sommeil</h4>
                        <p class="text-muted">Commencez par enregistrer votre première nuit de sommeil !</p>
                        <a href="{{ url_for('tracker') }}" class="btn btn-primary">
                            <i class="bi bi-plus-circle"></i>
                            Enregistrer ma première nuit
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Graphique simple (optionnel, peut être ajouté plus tard) -->
{% if records %}
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="bi bi-bar-chart"></i>
                    Tendance des 7 derniers jours
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    {% for record in records[:7] %}
                    <div class="col">
                        <div class="text-center">
                            <div class="progress mb-2" style="height: 100px; writing-mode: vertical-lr;">
                                {% set height = (record.sleep_duration / 12 * 100) | round %}
                                <div class="progress-bar bg-primary" 
                                     style="height: {{ height }}%"
                                     title="{{ record.sleep_duration }}h">
                                </div>
                            </div>
                            <small class="text-muted">
                                {{ record.date[-5:] }}<br>
                                {{ record.sleep_duration }}h
                            </small>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<script>
// Animation pour les cartes de statistiques
document.addEventListener('DOMContentLoaded', function() {
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            card.style.transition = 'all 0.5s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
});
</script>
{% endblock %}
