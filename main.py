import telebot
from telebot.types import Message
from datetime import datetime, timedelta

TOKEN = "TON_BOT_TOKEN_ICI"
bot = telebot.TeleBot(TOKEN)

user_data = {}

def sommeil_recommande(age: int):
    if age < 0:
        return "⛔ Âge invalide.", 0, 0
    elif age <= 1:
        return "🍼 Bébés : 14 à 17 heures", 14, 17
    elif 1 < age <= 2:
        return "👶 Tout-petits : 11 à 14 heures", 11, 14
    elif 3 <= age <= 5:
        return "🧒 Préscolaire : 10 à 13 heures", 10, 13
    elif 6 <= age <= 13:
        return "👧👦 Scolaire : 9 à 11 heures", 9, 11
    elif 14 <= age <= 17:
        return "🧑 Ados : 8 à 10 heures", 8, 10
    elif 18 <= age <= 64:
        return "🧔 Adultes : 7 à 9 heures", 7, 9
    elif age >= 65:
        return "👴 Seniors : 7 à 8 heures", 7, 8
    else:
        return "Âge non reconnu", 0, 0

@bot.message_handler(commands=['start', 'help'])
def start(message: Message):
    bot.send_message(message.chat.id, "👋 Salut ! Dis-moi d'abord ton âge.")
    user_data[message.chat.id] = {}

@bot.message_handler(func=lambda m: m.chat.id in user_data and 'age' not in user_data[m.chat.id])
def get_age(message: Message):
    try:
        age = int(message.text)
        user_data[message.chat.id]['age'] = age
        bot.send_message(message.chat.id, "🛌 À quelle heure t'es-tu couché hier soir ? (ex: 23:30)")
    except:
        bot.send_message(message.chat.id, "⚠️ Veuillez entrer un âge valide.")

@bot.message_handler(func=lambda m: m.chat.id in user_data and 'coucher' not in user_data[m.chat.id])
def get_coucher(message: Message):
    try:
        heure = datetime.strptime(message.text.strip(), "%H:%M").time()
        user_data[message.chat.id]['coucher'] = heure
        bot.send_message(message.chat.id, "⏰ À quelle heure t'es-tu réveillé ce matin ? (ex: 07:30)")
    except:
        bot.send_message(message.chat.id, "⚠️ Heure invalide. Ex: 23:15")

@bot.message_handler(func=lambda m: m.chat.id in user_data and 'reveil' not in user_data[m.chat.id])
def get_reveil(message: Message):
    try:
        heure_reveil = datetime.strptime(message.text.strip(), "%H:%M").time()
        data = user_data[m.chat.id]

        dt_coucher = datetime.combine(datetime.today(), data['coucher']) - timedelta(days=1)
        dt_reveil = datetime.combine(datetime.today(), heure_reveil)

        duree = dt_reveil - dt_coucher
        heures = round(duree.total_seconds() / 3600, 2)

        label, min_rec, max_rec = sommeil_recommande(data['age'])

        message_final = f"😴 Tu as dormi environ **{heures} heures**.\n"
        message_final += f"Recommandé pour ton âge : {label}.\n\n"

        if heures < min_rec:
            message_final += "⚠️ Tu n’as pas dormi assez cette nuit.\n"
        else:
            message_final += "✅ Bien joué ! Tu as eu un bon sommeil.\n"

        # 🔮 Calcul du coucher recommandé ce soir
        dt_reveil = datetime.combine(datetime.today(), heure_reveil)
        heure_coucher_min = dt_reveil - timedelta(hours=max_rec)
        heure_coucher_max = dt_reveil - timedelta(hours=min_rec)

        message_final += "\n🕰️ Pour bien dormir cette nuit, essaie de dormir entre "
        message_final += f"**{heure_coucher_min.strftime('%H:%M')}** et **{heure_coucher_max.strftime('%H:%M')}**."

        bot.send_message(m.chat.id, message_final, parse_mode='Markdown')
        user_data.pop(m.chat.id)
    except:
        bot.send_message(message.chat.id, "⚠️ Heure invalide. Ex: 07:45")

bot.polling()
