{% extends "base.html" %}

{% block title %}Résultats - Track<PERSON> de Sommeil{% endblock %}

{% block content %}
<div class="text-center mb-5">
    <h1 class="display-5 fw-bold text-primary mb-3">
        <i class="bi bi-graph-up"></i>
        Analyse de votre sommeil
    </h1>
    <p class="lead text-muted">
        Voici l'analyse détaillée de votre nuit de sommeil
    </p>
</div>

<!-- Résumé principal -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-primary">
            <div class="card-header bg-primary text-white text-center">
                <h4 class="mb-0">
                    <i class="bi bi-moon-stars"></i>
                    Résumé de votre nuit
                </h4>
            </div>
            <div class="card-body text-center">
                <div class="row">
                    <div class="col-md-3">
                        <div class="mb-3">
                            <i class="bi bi-person-circle text-info" style="font-size: 2rem;"></i>
                            <h6 class="mt-2">Âge</h6>
                            <span class="badge bg-info fs-6">{{ age }} ans</span>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <i class="bi bi-moon text-dark" style="font-size: 2rem;"></i>
                            <h6 class="mt-2">Coucher</h6>
                            <span class="badge bg-dark fs-6">{{ heure_coucher }}</span>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <i class="bi bi-sun text-warning" style="font-size: 2rem;"></i>
                            <h6 class="mt-2">Réveil</h6>
                            <span class="badge bg-warning fs-6">{{ heure_reveil }}</span>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <i class="bi bi-clock text-primary" style="font-size: 2rem;"></i>
                            <h6 class="mt-2">Durée</h6>
                            <span class="badge bg-primary fs-6">{{ heures_dormies }}h</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Évaluation du sommeil -->
<div class="row mb-4">
    <div class="col-12">
        <div class="alert {{ classe_css }} alert-dismissible fade show" role="alert">
            <h5 class="alert-heading">
                <i class="bi bi-clipboard-check"></i>
                Évaluation de votre sommeil
            </h5>
            <p class="mb-2">
                <strong>Vous avez dormi {{ heures_dormies }} heures cette nuit.</strong>
            </p>
            <p class="mb-2">{{ recommandation }}</p>
            <hr>
            <p class="mb-0">{{ evaluation }}</p>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    </div>
</div>

<!-- Recommandations pour ce soir -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-success">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">
                    <i class="bi bi-lightbulb"></i>
                    Recommandations pour ce soir
                </h5>
            </div>
            <div class="card-body">
                <div class="text-center">
                    <p class="lead">
                        Pour optimiser votre sommeil cette nuit, nous vous recommandons de vous coucher entre :
                    </p>
                    <div class="row justify-content-center">
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h3 class="text-success">
                                        <i class="bi bi-clock"></i>
                                        {{ coucher_min }} - {{ coucher_max }}
                                    </h3>
                                    <p class="text-muted mb-0">
                                        Heure de coucher recommandée
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Conseils personnalisés -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-heart"></i>
                    Conseils pour améliorer votre sommeil
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    {% if heures_dormies < 7 %}
                    <div class="col-md-6">
                        <h6><i class="bi bi-exclamation-triangle text-warning"></i> Manque de sommeil détecté</h6>
                        <ul class="list-unstyled">
                            <li><i class="bi bi-check text-success"></i> Essayez de vous coucher plus tôt ce soir</li>
                            <li><i class="bi bi-check text-success"></i> Évitez les écrans 1h avant le coucher</li>
                            <li><i class="bi bi-check text-success"></i> Créez un environnement propice au sommeil</li>
                        </ul>
                    </div>
                    {% elif heures_dormies > 9 %}
                    <div class="col-md-6">
                        <h6><i class="bi bi-info-circle text-info"></i> Sommeil prolongé</h6>
                        <ul class="list-unstyled">
                            <li><i class="bi bi-check text-success"></i> Vérifiez la qualité de votre sommeil</li>
                            <li><i class="bi bi-check text-success"></i> Maintenez des horaires réguliers</li>
                            <li><i class="bi bi-check text-success"></i> Exposez-vous à la lumière le matin</li>
                        </ul>
                    </div>
                    {% else %}
                    <div class="col-md-6">
                        <h6><i class="bi bi-check-circle text-success"></i> Excellent sommeil !</h6>
                        <ul class="list-unstyled">
                            <li><i class="bi bi-check text-success"></i> Continuez vos bonnes habitudes</li>
                            <li><i class="bi bi-check text-success"></i> Maintenez des horaires réguliers</li>
                            <li><i class="bi bi-check text-success"></i> Gardez un environnement de sommeil optimal</li>
                        </ul>
                    </div>
                    {% endif %}
                    
                    <div class="col-md-6">
                        <h6><i class="bi bi-gear text-primary"></i> Conseils généraux</h6>
                        <ul class="list-unstyled">
                            <li><i class="bi bi-check text-success"></i> Température idéale : 18-20°C</li>
                            <li><i class="bi bi-check text-success"></i> Évitez la caféine après 14h</li>
                            <li><i class="bi bi-check text-success"></i> Pratiquez une activité relaxante avant le coucher</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Actions -->
<div class="row">
    <div class="col-12 text-center">
        <a href="{{ url_for('tracker') }}" class="btn btn-primary btn-lg me-3">
            <i class="bi bi-arrow-clockwise"></i>
            Nouvelle analyse
        </a>
        <a href="{{ url_for('info') }}" class="btn btn-outline-info btn-lg">
            <i class="bi bi-book"></i>
            En savoir plus
        </a>
    </div>
</div>

<script>
// Animation d'entrée pour les cartes
document.addEventListener('DOMContentLoaded', function() {
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            card.style.transition = 'all 0.5s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 200);
    });
});
</script>
{% endblock %}
