{% extends "base.html" %}

{% block title %}Tracker - <PERSON><PERSON> de Sommeil{% endblock %}

{% block content %}
<div class="text-center mb-5">
    <h1 class="display-5 fw-bold text-primary mb-3">
        <i class="bi bi-clock-history"></i>
        Analysez votre sommeil
    </h1>
    <p class="lead text-muted">
        Remplissez le formulaire ci-dessous pour obtenir une analyse de votre sommeil
    </p>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-body p-4">
                <form method="POST" id="sleepForm">
                    <!-- Âge -->
                    <div class="mb-4">
                        <label for="age" class="form-label fw-bold">
                            <i class="bi bi-person"></i>
                            Votre âge
                        </label>
                        <input type="number" 
                               class="form-control form-control-lg" 
                               id="age" 
                               name="age" 
                               min="0" 
                               max="120" 
                               required
                               placeholder="Ex: 25">
                        <div class="form-text">
                            <i class="bi bi-info-circle"></i>
                            Votre âge nous aide à vous donner des recommandations personnalisées
                        </div>
                    </div>

                    <!-- Heure de coucher -->
                    <div class="mb-4">
                        <label for="heure_coucher" class="form-label fw-bold">
                            <i class="bi bi-moon"></i>
                            À quelle heure vous êtes-vous couché(e) hier soir ?
                        </label>
                        <input type="time" 
                               class="form-control form-control-lg" 
                               id="heure_coucher" 
                               name="heure_coucher" 
                               required>
                        <div class="form-text">
                            <i class="bi bi-info-circle"></i>
                            L'heure à laquelle vous vous êtes mis(e) au lit hier soir
                        </div>
                    </div>

                    <!-- Heure de réveil -->
                    <div class="mb-4">
                        <label for="heure_reveil" class="form-label fw-bold">
                            <i class="bi bi-sun"></i>
                            À quelle heure vous êtes-vous réveillé(e) ce matin ?
                        </label>
                        <input type="time" 
                               class="form-control form-control-lg" 
                               id="heure_reveil" 
                               name="heure_reveil" 
                               required>
                        <div class="form-text">
                            <i class="bi bi-info-circle"></i>
                            L'heure à laquelle vous vous êtes réveillé(e) ce matin
                        </div>
                    </div>

                    <!-- Bouton de soumission -->
                    <div class="text-center">
                        <button type="submit" class="btn btn-primary btn-lg px-5">
                            <i class="bi bi-calculator"></i>
                            Analyser mon sommeil
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Section d'aide -->
<div class="row mt-5">
    <div class="col-12">
        <div class="card bg-light">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="bi bi-question-circle"></i>
                    Comment bien remplir le formulaire ?
                </h5>
                <div class="row">
                    <div class="col-md-4">
                        <h6><i class="bi bi-1-circle text-primary"></i> Votre âge</h6>
                        <p class="small text-muted">
                            Entrez votre âge actuel. Cela nous permet de vous donner des recommandations 
                            adaptées selon les standards médicaux.
                        </p>
                    </div>
                    <div class="col-md-4">
                        <h6><i class="bi bi-2-circle text-primary"></i> Heure de coucher</h6>
                        <p class="small text-muted">
                            L'heure à laquelle vous vous êtes mis(e) au lit hier soir, 
                            pas forcément l'heure d'endormissement.
                        </p>
                    </div>
                    <div class="col-md-4">
                        <h6><i class="bi bi-3-circle text-primary"></i> Heure de réveil</h6>
                        <p class="small text-muted">
                            L'heure à laquelle vous vous êtes réveillé(e) ce matin, 
                            même si vous êtes resté(e) au lit après.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Validation côté client
document.getElementById('sleepForm').addEventListener('submit', function(e) {
    const age = document.getElementById('age').value;
    const coucher = document.getElementById('heure_coucher').value;
    const reveil = document.getElementById('heure_reveil').value;
    
    if (!age || !coucher || !reveil) {
        e.preventDefault();
        alert('⚠️ Veuillez remplir tous les champs.');
        return;
    }
    
    if (age < 0 || age > 120) {
        e.preventDefault();
        alert('⚠️ Veuillez entrer un âge valide (0-120 ans).');
        return;
    }
    
    // Vérification basique des heures
    const heureCoucher = new Date('2000-01-01 ' + coucher);
    const heureReveil = new Date('2000-01-02 ' + reveil);
    
    if (heureCoucher >= heureReveil) {
        // Si l'heure de coucher est après l'heure de réveil, on assume que c'est le jour suivant
        console.log('Coucher après minuit détecté');
    }
});

// Pré-remplir avec des valeurs par défaut si souhaité
document.addEventListener('DOMContentLoaded', function() {
    // Vous pouvez ajouter des valeurs par défaut ici si nécessaire
});
</script>
{% endblock %}
