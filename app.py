from flask import Flask, render_template, request, redirect, url_for, flash
from datetime import datetime, timedelta
import sqlite3

app = Flask(__name__)
app.secret_key = 'votre_cle_secrete_ici'  # Changez ceci en production

# Configuration de la base de données
DATABASE = 'sleep_tracker.db'

def init_db():
    """Initialise la base de données"""
    conn = sqlite3.connect(DATABASE)
    cursor = conn.cursor()

    # Table des utilisateurs
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            email TEXT,
            age INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')

    # Table des enregistrements de sommeil
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS sleep_records (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER,
            date DATE NOT NULL,
            bedtime TIME NOT NULL,
            wake_time TIME NOT NULL,
            sleep_duration REAL,
            sleep_quality INTEGER,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
    ''')

    # Table des rappels
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS reminders (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER,
            reminder_time TIME NOT NULL,
            is_active BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
    ''')

    conn.commit()
    conn.close()

def get_db_connection():
    """Obtient une connexion à la base de données"""
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    return conn

def get_or_create_user(username, age=None):
    """Obtient ou crée un utilisateur"""
    conn = get_db_connection()

    # Vérifier si l'utilisateur existe
    user = conn.execute('SELECT * FROM users WHERE username = ?', (username,)).fetchone()

    if user is None:
        # Créer un nouvel utilisateur
        cursor = conn.cursor()
        cursor.execute('INSERT INTO users (username, age) VALUES (?, ?)', (username, age))
        user_id = cursor.lastrowid
        conn.commit()
        user = conn.execute('SELECT * FROM users WHERE id = ?', (user_id,)).fetchone()

    conn.close()
    return user

def save_sleep_record(user_id, date, bedtime, wake_time, sleep_duration, sleep_quality=None, notes=None):
    """Sauvegarde un enregistrement de sommeil"""
    conn = get_db_connection()

    # Vérifier si un enregistrement existe déjà pour cette date
    existing = conn.execute(
        'SELECT id FROM sleep_records WHERE user_id = ? AND date = ?',
        (user_id, date)
    ).fetchone()

    if existing:
        # Mettre à jour l'enregistrement existant
        conn.execute('''
            UPDATE sleep_records
            SET bedtime = ?, wake_time = ?, sleep_duration = ?, sleep_quality = ?, notes = ?
            WHERE user_id = ? AND date = ?
        ''', (bedtime, wake_time, sleep_duration, sleep_quality, notes, user_id, date))
    else:
        # Créer un nouvel enregistrement
        conn.execute('''
            INSERT INTO sleep_records (user_id, date, bedtime, wake_time, sleep_duration, sleep_quality, notes)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (user_id, date, bedtime, wake_time, sleep_duration, sleep_quality, notes))

    conn.commit()
    conn.close()

def get_user_sleep_records(user_id, limit=30):
    """Récupère les enregistrements de sommeil d'un utilisateur"""
    conn = get_db_connection()
    records = conn.execute('''
        SELECT * FROM sleep_records
        WHERE user_id = ?
        ORDER BY date DESC
        LIMIT ?
    ''', (user_id, limit)).fetchall()
    conn.close()
    return records

# Initialiser la base de données au démarrage
init_db()

def sommeil_recommande(age: int):
    """Retourne les recommandations de sommeil selon l'âge"""
    if age < 0:
        return "⛔ Âge invalide.", 0, 0
    elif age <= 1:
        return "🍼 Bébés : 14 à 17 heures", 14, 17
    elif 1 < age <= 2:
        return "👶 Tout-petits : 11 à 14 heures", 11, 14
    elif 3 <= age <= 5:
        return "🧒 Préscolaire : 10 à 13 heures", 10, 13
    elif 6 <= age <= 13:
        return "👧👦 Scolaire : 9 à 11 heures", 9, 11
    elif 14 <= age <= 17:
        return "🧑 Ados : 8 à 10 heures", 8, 10
    elif 18 <= age <= 64:
        return "🧔 Adultes : 7 à 9 heures", 7, 9
    elif age >= 65:
        return "👴 Seniors : 7 à 8 heures", 7, 8
    else:
        return "Âge non reconnu", 0, 0

def calculer_sommeil(age, heure_coucher, heure_reveil):
    """Calcule la durée de sommeil et les recommandations"""
    try:
        # Convertir les heures en objets datetime
        dt_coucher = datetime.combine(datetime.today(), heure_coucher) - timedelta(days=1)
        dt_reveil = datetime.combine(datetime.today(), heure_reveil)
        
        # Calculer la durée
        duree = dt_reveil - dt_coucher
        heures = round(duree.total_seconds() / 3600, 2)
        
        # Obtenir les recommandations
        label, min_rec, max_rec = sommeil_recommande(age)
        
        # Évaluer la qualité du sommeil
        if heures < min_rec:
            evaluation = "⚠️ Tu n'as pas dormi assez cette nuit."
            classe_css = "alert-warning"
        elif heures > max_rec:
            evaluation = "😴 Tu as peut-être trop dormi."
            classe_css = "alert-info"
        else:
            evaluation = "✅ Bien joué ! Tu as eu un bon sommeil."
            classe_css = "alert-success"
        
        # Calculer les heures de coucher recommandées pour ce soir
        dt_reveil_demain = datetime.combine(datetime.today() + timedelta(days=1), heure_reveil)
        heure_coucher_min = dt_reveil_demain - timedelta(hours=max_rec)
        heure_coucher_max = dt_reveil_demain - timedelta(hours=min_rec)
        
        return {
            'heures_dormies': heures,
            'recommandation': label,
            'evaluation': evaluation,
            'classe_css': classe_css,
            'coucher_min': heure_coucher_min.strftime('%H:%M'),
            'coucher_max': heure_coucher_max.strftime('%H:%M')
        }
    except Exception:
        return None

@app.route('/')
def index():
    """Page d'accueil"""
    return render_template('index.html')

@app.route('/tracker', methods=['GET', 'POST'])
def tracker():
    """Page principale du tracker de sommeil"""
    if request.method == 'POST':
        try:
            # Récupérer les données du formulaire
            username = request.form.get('username', 'utilisateur_anonyme')
            age = int(request.form['age'])
            heure_coucher_str = request.form['heure_coucher']
            heure_reveil_str = request.form['heure_reveil']
            qualite = int(request.form.get('qualite', 3))  # 1-5 scale
            notes = request.form.get('notes', '')

            # Valider l'âge
            if age < 0 or age > 120:
                flash('⚠️ Veuillez entrer un âge valide (0-120 ans).', 'error')
                return render_template('tracker.html')

            # Convertir les heures
            heure_coucher = datetime.strptime(heure_coucher_str, "%H:%M").time()
            heure_reveil = datetime.strptime(heure_reveil_str, "%H:%M").time()

            # Calculer les résultats
            resultats = calculer_sommeil(age, heure_coucher, heure_reveil)

            if resultats:
                # Sauvegarder dans la base de données
                user = get_or_create_user(username, age)
                date_today = datetime.now().date()

                save_sleep_record(
                    user['id'],
                    date_today,
                    heure_coucher,
                    heure_reveil,
                    resultats['heures_dormies'],
                    qualite,
                    notes
                )

                flash('✅ Données sauvegardées avec succès !', 'success')

                return render_template('resultats.html',
                                     username=username,
                                     age=age,
                                     heure_coucher=heure_coucher_str,
                                     heure_reveil=heure_reveil_str,
                                     **resultats)
            else:
                flash('⚠️ Erreur dans le calcul. Vérifiez vos heures.', 'error')

        except ValueError:
            flash('⚠️ Veuillez entrer des données valides.', 'error')
        except Exception:
            flash('⚠️ Une erreur s\'est produite. Veuillez réessayer.', 'error')

    return render_template('tracker.html')

@app.route('/suivi')
@app.route('/suivi/<username>')
def suivi(username=None):
    """Page de suivi du sommeil"""
    if not username:
        return render_template('suivi_login.html')

    # Récupérer l'utilisateur
    conn = get_db_connection()
    user = conn.execute('SELECT * FROM users WHERE username = ?', (username,)).fetchone()

    if not user:
        flash('⚠️ Utilisateur non trouvé.', 'error')
        return redirect(url_for('suivi'))

    # Récupérer les enregistrements de sommeil
    records = get_user_sleep_records(user['id'], 30)

    # Calculer les statistiques
    if records:
        durees = [r['sleep_duration'] for r in records if r['sleep_duration']]
        moyenne_sommeil = sum(durees) / len(durees) if durees else 0

        qualites = [r['sleep_quality'] for r in records if r['sleep_quality']]
        moyenne_qualite = sum(qualites) / len(qualites) if qualites else 0
    else:
        moyenne_sommeil = 0
        moyenne_qualite = 0

    conn.close()

    return render_template('suivi.html',
                         user=user,
                         records=records,
                         moyenne_sommeil=round(moyenne_sommeil, 1),
                         moyenne_qualite=round(moyenne_qualite, 1))

@app.route('/rappels')
@app.route('/rappels/<username>')
def rappels(username=None):
    """Page de gestion des rappels"""
    if not username:
        return render_template('rappels_login.html')

    # Récupérer l'utilisateur
    conn = get_db_connection()
    user = conn.execute('SELECT * FROM users WHERE username = ?', (username,)).fetchone()

    if not user:
        flash('⚠️ Utilisateur non trouvé.', 'error')
        return redirect(url_for('rappels'))

    # Récupérer les rappels
    user_reminders = conn.execute(
        'SELECT * FROM reminders WHERE user_id = ? ORDER BY reminder_time',
        (user['id'],)
    ).fetchall()

    conn.close()

    return render_template('rappels.html', user=user, reminders=user_reminders)

@app.route('/add_reminder', methods=['POST'])
def add_reminder():
    """Ajouter un rappel"""
    try:
        username = request.form['username']
        reminder_time = request.form['reminder_time']

        # Récupérer l'utilisateur
        conn = get_db_connection()
        user = conn.execute('SELECT * FROM users WHERE username = ?', (username,)).fetchone()

        if user:
            # Ajouter le rappel
            conn.execute(
                'INSERT INTO reminders (user_id, reminder_time) VALUES (?, ?)',
                (user['id'], reminder_time)
            )
            conn.commit()
            flash('✅ Rappel ajouté avec succès !', 'success')
        else:
            flash('⚠️ Utilisateur non trouvé.', 'error')

        conn.close()

    except Exception:
        flash('⚠️ Erreur lors de l\'ajout du rappel.', 'error')

    return redirect(url_for('rappels', username=username))

@app.route('/toggle_reminder/<int:reminder_id>')
def toggle_reminder(reminder_id):
    """Activer/désactiver un rappel"""
    try:
        conn = get_db_connection()
        reminder = conn.execute('SELECT * FROM reminders WHERE id = ?', (reminder_id,)).fetchone()

        if reminder:
            new_status = not reminder['is_active']
            conn.execute(
                'UPDATE reminders SET is_active = ? WHERE id = ?',
                (new_status, reminder_id)
            )
            conn.commit()

            # Récupérer le nom d'utilisateur pour la redirection
            user = conn.execute('SELECT username FROM users WHERE id = ?', (reminder['user_id'],)).fetchone()
            username = user['username'] if user else None

            flash('✅ Rappel mis à jour !', 'success')

        conn.close()

    except Exception:
        flash('⚠️ Erreur lors de la mise à jour.', 'error')
        username = None

    return redirect(url_for('rappels', username=username))

@app.route('/info')
def info():
    """Page d'informations sur le sommeil"""
    return render_template('info.html')

if __name__ == '__main__':
    try:
        app.run(debug=True, host='127.0.0.1', port=8080)
    except OSError:
        print("Erreur de port: Essayons un autre port...")
        app.run(debug=True, host='127.0.0.1', port=8082)
