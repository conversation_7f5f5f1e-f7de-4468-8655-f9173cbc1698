from flask import Flask, render_template, request, redirect, url_for, flash, session
from datetime import datetime, timedelta
import uuid

app = Flask(__name__)
app.secret_key = 'votre_cle_secrete_ici'  # Changez ceci en production

def sommeil_recommande(age: int):
    """Retourne les recommandations de sommeil selon l'âge"""
    if age < 0:
        return "⛔ Âge invalide.", 0, 0
    elif age <= 1:
        return "🍼 Bébés : 14 à 17 heures", 14, 17
    elif 1 < age <= 2:
        return "👶 Tout-petits : 11 à 14 heures", 11, 14
    elif 3 <= age <= 5:
        return "🧒 Préscolaire : 10 à 13 heures", 10, 13
    elif 6 <= age <= 13:
        return "👧👦 Scolaire : 9 à 11 heures", 9, 11
    elif 14 <= age <= 17:
        return "🧑 Ados : 8 à 10 heures", 8, 10
    elif 18 <= age <= 64:
        return "🧔 Adultes : 7 à 9 heures", 7, 9
    elif age >= 65:
        return "👴 Seniors : 7 à 8 heures", 7, 8
    else:
        return "Âge non reconnu", 0, 0

def calculer_sommeil(age, heure_coucher, heure_reveil):
    """Calcule la durée de sommeil et les recommandations"""
    try:
        # Convertir les heures en objets datetime
        dt_coucher = datetime.combine(datetime.today(), heure_coucher) - timedelta(days=1)
        dt_reveil = datetime.combine(datetime.today(), heure_reveil)
        
        # Calculer la durée
        duree = dt_reveil - dt_coucher
        heures = round(duree.total_seconds() / 3600, 2)
        
        # Obtenir les recommandations
        label, min_rec, max_rec = sommeil_recommande(age)
        
        # Évaluer la qualité du sommeil
        if heures < min_rec:
            evaluation = "⚠️ Tu n'as pas dormi assez cette nuit."
            classe_css = "alert-warning"
        elif heures > max_rec:
            evaluation = "😴 Tu as peut-être trop dormi."
            classe_css = "alert-info"
        else:
            evaluation = "✅ Bien joué ! Tu as eu un bon sommeil."
            classe_css = "alert-success"
        
        # Calculer les heures de coucher recommandées pour ce soir
        dt_reveil_demain = datetime.combine(datetime.today() + timedelta(days=1), heure_reveil)
        heure_coucher_min = dt_reveil_demain - timedelta(hours=max_rec)
        heure_coucher_max = dt_reveil_demain - timedelta(hours=min_rec)
        
        return {
            'heures_dormies': heures,
            'recommandation': label,
            'evaluation': evaluation,
            'classe_css': classe_css,
            'coucher_min': heure_coucher_min.strftime('%H:%M'),
            'coucher_max': heure_coucher_max.strftime('%H:%M')
        }
    except Exception as e:
        return None

@app.route('/')
def index():
    """Page d'accueil"""
    return render_template('index.html')

@app.route('/tracker', methods=['GET', 'POST'])
def tracker():
    """Page principale du tracker de sommeil"""
    if request.method == 'POST':
        try:
            # Récupérer les données du formulaire
            age = int(request.form['age'])
            heure_coucher_str = request.form['heure_coucher']
            heure_reveil_str = request.form['heure_reveil']
            
            # Valider l'âge
            if age < 0 or age > 120:
                flash('⚠️ Veuillez entrer un âge valide (0-120 ans).', 'error')
                return render_template('tracker.html')
            
            # Convertir les heures
            heure_coucher = datetime.strptime(heure_coucher_str, "%H:%M").time()
            heure_reveil = datetime.strptime(heure_reveil_str, "%H:%M").time()
            
            # Calculer les résultats
            resultats = calculer_sommeil(age, heure_coucher, heure_reveil)
            
            if resultats:
                return render_template('resultats.html', 
                                     age=age,
                                     heure_coucher=heure_coucher_str,
                                     heure_reveil=heure_reveil_str,
                                     **resultats)
            else:
                flash('⚠️ Erreur dans le calcul. Vérifiez vos heures.', 'error')
                
        except ValueError:
            flash('⚠️ Veuillez entrer des données valides.', 'error')
        except Exception as e:
            flash('⚠️ Une erreur s\'est produite. Veuillez réessayer.', 'error')
    
    return render_template('tracker.html')

@app.route('/info')
def info():
    """Page d'informations sur le sommeil"""
    return render_template('info.html')

if __name__ == '__main__':
    app.run(debug=True)
